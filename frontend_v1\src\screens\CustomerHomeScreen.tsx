import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { ScrollView, View } from 'react-native';
import { StyleSheet, RefreshControl, Alert, SafeAreaView,  } from 'react-native';
// Import components
import { FocusableButton } from '../components/accessibility/FocusableButton';
import { Heading, Body } from '../components/typography/Typography';
import { ResponsiveContainer } from '../components/layout/ResponsiveContainer';
// import { useHighContrastColors } from '../contexts/HighContrastContext';
// import { useSimplifiedText, useCognitiveAccessibility } from '../contexts/CognitiveAccessibilityContext';
// import { useResponsive } from '../contexts/ResponsiveContext';

// Import types and utilities
import { ScreenReaderUtils } from '../utils/accessibilityUtils';

interface Category {
  id: string;
  name: string;
  category: string;
  color: string;
  serviceCount: number;
  icon: string;
}

const CustomerHomeScreen: React.FC = () => {
  // const { colors } = useHighContrastColors();
  // const { } = useCognitiveAccessibility();
  // const { isTablet, getResponsiveValue } = useResponsive();
  // Temporary fallback values
  const colors = {
    background: '#FFFFFF',
    text: {
      primary: '#000000',
      secondary: '#666666',
      tertiary: '#999999'
    }
  };
  const isTablet = false;
  const getResponsiveValue = (values: any) => {
    if (typeof values === 'object' && values.xs) {
      return values.xs;
    }
    return values;
  };
  const styles = useMemo(() => createStyles(colors), [colors]);

  // Simplified text hooks for cognitive accessibility
  // const simplifiedGreeting = useSimplifiedText('Good morning');
  // const simplifiedBrowseTitle = useSimplifiedText('Browse Services');
  // const simplifiedFeaturedTitle = useSimplifiedText('Featured Providers');
  // const simplifiedFavoriteTitle = useSimplifiedText('Favorite Providers');

  // Temporary fallback values for simplified text
  const simplifiedGreeting = 'Good morning';
  const simplifiedBrowseTitle = 'Browse Services';
  const simplifiedFeaturedTitle = 'Featured Providers';
  const simplifiedFavoriteTitle = 'Favorite Providers';
  // const simplifiedNearbyTitle = useSimplifiedText('Nearby Providers');
  const simplifiedNearbyTitle = 'Nearby Providers';

  // Local state
  const [refreshing, setRefreshing] = useState(false);

  // Categories data
  const categories: Category[] = [
    {
      id: '1',
      name: 'Barber',
      category: 'barber',
      color: '#5A7A63',
      serviceCount: 12,
      icon: 'cut-outline',
    },
    {
      id: '2',
      name: 'Salon',
      category: 'salon',
      color: '#6B8A74',
      serviceCount: 8,
      icon: 'brush-outline',
    },
    {
      id: '3',
      name: 'Nail Services',
      category: 'nail-services',
      color: '#5A7A63',
      serviceCount: 15,
      icon: 'hand-left-outline',
    },
    {
      id: '4',
      name: 'Lash Services',
      category: 'lash-services',
      color: '#4A6B52',
      serviceCount: 6,
      icon: 'eye-outline',
    },
    {
      id: '5',
      name: 'Braiding',
      category: 'braiding',
      color: '#3A5B42',
      serviceCount: 10,
      icon: 'flower-outline',
    },
    {
      id: '6',
      name: 'Skincare',
      category: 'skincare',
      color: '#6B8A74',
      serviceCount: 7,
      icon: 'heart-outline',
    },
    {
      id: '7',
      name: 'Massage',
      category: 'massage',
      color: '#5A7A63',
      serviceCount: 8,
      icon: 'hand-right-outline',
    },
  ];

  // Refresh handler
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simulate data refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Error refreshing data:', error);
      Alert.alert('Error', 'Failed to refresh data. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Navigation handlers
  const handleCategoryPress = useCallback((category: string) => {
    console.log('Category pressed:', category);
  }, []);



  // Announce screen content for screen readers
  useEffect(() => {
    const announceScreenContent = async () => {
      const isScreenReaderEnabled = await ScreenReaderUtils.isScreenReaderEnabled();
      if (isScreenReaderEnabled) {
        // Delay announcement to allow screen to fully load
        setTimeout(() => {
          ScreenReaderUtils.announceForAccessibility(
            `Customer Home Screen loaded. Good morning User. Browse ${categories.length} service categories including ${categories.map(c => c.name).join(', ')}.`
          );
        }, 1000);
      }
    };

    announceScreenContent();
  }, [categories]);

  return (
    <SafeAreaView
      style={styles.container}
      accessibilityRole="none"
      accessibilityLabel="Customer Home Screen"
    >
      <ResponsiveContainer
        maxWidth={isTablet ? 'lg' : 'full'}
        padding={getResponsiveValue({ xs: 'sm', md: 'md', lg: 'lg' })}
        centerContent={isTablet}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#5A7A63']}
            tintColor={'#5A7A63'}
            accessibilityLabel={refreshing ? "Refreshing content" : "Pull to refresh"}
          />
        }
        showsVerticalScrollIndicator={false}
        accessibilityRole="scrollbar"
        accessibilityLabel="Main content area"
        accessibilityHint="Scroll to browse services and providers"
      >
        {/* Welcome Section */}
        <View
          style={styles.welcomeSection}
          accessibilityRole="none"
          accessibilityLabel="Welcome section"
        >
          <Body
            color={colors?.text?.secondary}
            accessibilityLabel={`Greeting: ${simplifiedGreeting}`}
          >
            {simplifiedGreeting}
          </Body>
          <Heading
            level={3}
            color={colors?.text?.primary}
            accessibilityLabel="Welcome User, main heading"
          >
            User
          </Heading>
        </View>

        {/* Browse Services Section */}
        <View
          style={styles.section}
          accessibilityRole="none"
          accessibilityLabel="Browse Services section"
        >
          <Heading
            level={2}
            color={colors?.text?.primary}
            accessibilityLabel={`${simplifiedBrowseTitle}, section heading`}
          >
            {simplifiedBrowseTitle}
          </Heading>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoriesScroll}
            accessibilityRole="list"
            accessibilityLabel={`Service categories, ${categories.length} categories available`}
            accessibilityHint="Swipe left or right to browse service categories"
          >
            <View style={styles.categoriesRow}>
              {categories.map((category) => (
                <View
                  key={category.id}
                  style={[styles.categoryCard, { backgroundColor: category.color }]}
                >
                  <FocusableButton
                    title={`${category.name}\n${category.serviceCount} services`}
                    onPress={() => handleCategoryPress(category.category)}
                    variant="ghost"
                    icon={category.icon}
                    accessibilityLabel={`${category.name} category, ${category.serviceCount} services available`}
                    accessibilityHint={`Tap to browse ${category.name} services`}
                    style={styles.categoryButton}
                    textStyle={styles.categoryButtonText}
                  />
                </View>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Featured Providers */}
        <View
          style={styles.section}
          accessibilityRole="none"
          accessibilityLabel="Featured Providers section"
        >
          <View style={styles.sectionHeader}>
            <Heading
              level={2}
              color={colors?.text?.primary}
              accessibilityLabel={`${simplifiedFeaturedTitle}, section heading`}
            >
              {simplifiedFeaturedTitle}
            </Heading>
            <FocusableButton
              title="See All"
              onPress={() => console.log('See all featured providers')}
              variant="ghost"
              size="small"
              accessibilityLabel="See all featured providers"
              accessibilityHint="Tap to view all featured providers"
              style={styles.seeAllButton}
              textStyle={styles.seeAllText}
            />
          </View>
          <Body
            color={colors?.text?.tertiary}
            align="center"
            accessibilityLabel="Featured providers will appear here, placeholder text"
          >
            Featured providers will appear here
          </Body>
        </View>

        {/* Favorite Providers */}
        <View
          style={styles.section}
          accessibilityRole="none"
          accessibilityLabel="Favorite Providers section"
        >
          <View style={styles.sectionHeader}>
            <Heading
              level={2}
              color={colors?.text?.primary}
              accessibilityLabel={`${simplifiedFavoriteTitle}, section heading`}
            >
              {simplifiedFavoriteTitle}
            </Heading>
            <FocusableButton
              title="See All"
              onPress={() => console.log('See all favorite providers')}
              variant="ghost"
              size="small"
              accessibilityLabel="See all favorite providers"
              accessibilityHint="Tap to view all your favorite providers"
              style={styles.seeAllButton}
              textStyle={styles.seeAllText}
            />
          </View>
          <Body
            color={colors?.text?.tertiary}
            align="center"
            accessibilityLabel="Your favorite providers will appear here, placeholder text"
          >
            Your favorite providers will appear here
          </Body>
        </View>

        {/* Nearby Providers */}
        <View
          style={styles.section}
          accessibilityRole="none"
          accessibilityLabel="Nearby Providers section"
        >
          <View style={styles.sectionHeader}>
            <Heading
              level={2}
              color={colors?.text?.primary}
              accessibilityLabel={`${simplifiedNearbyTitle}, section heading`}
            >
              {simplifiedNearbyTitle}
            </Heading>
            <FocusableButton
              title="See All"
              onPress={() => console.log('See all nearby providers')}
              variant="ghost"
              size="small"
              accessibilityLabel="See all nearby providers"
              accessibilityHint="Tap to view all providers near your location"
              style={styles.seeAllButton}
              textStyle={styles.seeAllText}
            />
          </View>
          <Body
            color={colors?.text?.tertiary}
            align="center"
            accessibilityLabel="Nearby providers will appear here, placeholder text"
          >
            Nearby providers will appear here
          </Body>
        </View>
      </ScrollView>
      </ResponsiveContainer>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100,
  },
  welcomeSection: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    backgroundColor: colors?.background?.primary || '#FFFFFF',
  },
  greeting: {
    fontSize: 16,
    color: colors?.text?.secondary || '#666',
    fontFamily: 'Inter-Regular',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#333',
    fontFamily: 'Inter-Bold',
    marginTop: 4,
  },

  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors?.text?.primary || '#333',
    fontFamily: 'Inter-Bold',
  },
  seeAllText: {
    fontSize: 14,
    color: colors?.primary?.default || '#5A7A63',
    fontFamily: 'Inter-Medium',
    fontWeight: '500',
  },
  categoriesScroll: {
    paddingLeft: 12,
  },
  categoriesRow: {
    flexDirection: 'row',
    paddingHorizontal: 4,
  },
  categoryCard: {
    width: 120,
    height: 100,
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryName: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
  },
  categoryCount: {
    color: '#FFFFFF',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.9,
  },
  placeholderText: {
    fontSize: 16,
    color: colors?.text?.tertiary || '#999',
    textAlign: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  categoryButton: {
    width: '100%',
    height: '100%',
    backgroundColor: 'transparent',
    borderRadius: 0,
  },
  categoryButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    textAlign: 'center',
    lineHeight: 16,
  },
  seeAllButton: {
    backgroundColor: 'transparent',
    minHeight: 32,
    paddingHorizontal: 8,
  },
});

export default CustomerHomeScreen;